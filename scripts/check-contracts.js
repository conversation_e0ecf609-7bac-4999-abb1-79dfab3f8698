const { ethers } = require("hardhat");

async function main() {
    console.log("🔍 Checking deployed contracts...");
    
    const DATACOIN_ADDRESS = "******************************************";
    const JOB_REWARD_SYSTEM_ADDRESS = "******************************************";
    
    try {
        // Get provider
        const provider = ethers.provider;
        console.log("Network:", await provider.getNetwork());
        
        // Check DATACOIN contract
        console.log("\n📋 Checking DATACOIN contract...");
        const datacoinCode = await provider.getCode(DATACOIN_ADDRESS);
        if (datacoinCode === "0x") {
            console.log("❌ DATACOIN contract not found at", DATACOIN_ADDRESS);
        } else {
            console.log("✅ DATACOIN contract exists");
            
            // Try to interact with contract
            const DATACOIN = await ethers.getContractFactory("DATACOIN");
            const datacoin = DATACOIN.attach(DATACOIN_ADDRESS);
            
            try {
                const name = await datacoin.name();
                const symbol = await datacoin.symbol();
                const owner = await datacoin.owner();
                console.log("   Name:", name);
                console.log("   Symbol:", symbol);
                console.log("   Owner:", owner);
            } catch (error) {
                console.log("   ⚠️ Error reading contract data:", error.message);
            }
        }
        
        // Check JobRewardSystem contract
        console.log("\n📋 Checking JobRewardSystem contract...");
        const jobSystemCode = await provider.getCode(JOB_REWARD_SYSTEM_ADDRESS);
        if (jobSystemCode === "0x") {
            console.log("❌ JobRewardSystem contract not found at", JOB_REWARD_SYSTEM_ADDRESS);
        } else {
            console.log("✅ JobRewardSystem contract exists");
            
            // Try to interact with contract
            const JobRewardSystem = await ethers.getContractFactory("JobRewardSystem");
            const jobRewardSystem = JobRewardSystem.attach(JOB_REWARD_SYSTEM_ADDRESS);
            
            try {
                const owner = await jobRewardSystem.owner();
                const systemEnabled = await jobRewardSystem.systemEnabled();
                const jobCounter = await jobRewardSystem.jobCounter();
                const minimumReward = await jobRewardSystem.minimumReward();
                
                console.log("   Owner:", owner);
                console.log("   System Enabled:", systemEnabled);
                console.log("   Job Counter:", jobCounter.toString());
                console.log("   Minimum Reward:", ethers.formatEther(minimumReward), "DTC");
                
                // Try to get system stats
                const stats = await jobRewardSystem.getSystemStats();
                console.log("   Total Jobs:", stats.totalJobs.toString());
                console.log("   Total Rewards:", ethers.formatEther(stats.totalRewards), "DTC");
                
            } catch (error) {
                console.log("   ⚠️ Error reading contract data:", error.message);
            }
        }
        
    } catch (error) {
        console.error("❌ Error checking contracts:", error.message);
    }
}

main()
    .then(() => {
        console.log("\n✅ Contract check completed!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("💥 Contract check failed:", error);
        process.exit(1);
    });
